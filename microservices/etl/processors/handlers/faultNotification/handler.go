package faultNotification

import (
	"context"
	"encoding/json"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing
type HandlerDeps struct {
	Connector     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	CreateService func(connections *connect.Connections) Service
}

// createHandler creates a handler with the given dependencies
func createHandler(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}

		svc := deps.CreateService(connections)
		sub := connections.Pubsub.Subscription(subscriptionName)

		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received fault notification message on %s with ID: %s", subscriptionName, string(msg.ID))

			// Parse the incoming fault notification message
			var faultMessage FaultNotificationMessage
			if err := json.Unmarshal(msg.Data, &faultMessage); err != nil {
				logger.Errorf("Failed to unmarshal fault notification message: %v", err)
				msg.Nack()
				return
			}

			// Validate required fields
			if faultMessage.DeviceId == "" || faultMessage.FaultReason == "" {
				logger.Errorf("Invalid fault notification message: missing required fields")
				msg.Ack() // Invalid message, don't retry
				return
			}

			// Process the fault notification
			if err := svc.ProcessFaultNotification(ctx, faultMessage); err != nil {
				logger.Errorf("Failed to process fault notification: %v", err)
				
				// Determine if error should be retried
				if isRetryableError(err) {
					msg.Nack()
					return
				}
				
				// Non-retryable error, acknowledge to prevent infinite retries
				msg.Ack()
				return
			}

			msg.Ack()
		})

		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", subscriptionName, err)
		}
	}
}

// createService injects dependencies into the service
func createService(connections *connect.Connections) Service {
	persistence := NewPersistenceRepository(connections.Postgres)
	return NewService(persistence, connections.Pubsub)
}

// isRetryableError determines if an error should trigger a retry
func isRetryableError(err error) bool {
	// Retry on database connection issues and pubsub publish failures
	switch err {
	case ErrUserLookupFailed, ErrPublishNotification:
		return true
	case ErrInvalidDeviceId, ErrMissingWebsiteAppsEnv, ErrInvalidFaultMessage:
		return false // Configuration or data issues shouldn't be retried
	default:
		return false
	}
}

// Handler is the production-ready handler with real dependencies
var Handler = createHandler(HandlerDeps{
	Connector:     connect.GetConnections,
	CreateService: createService,
})
