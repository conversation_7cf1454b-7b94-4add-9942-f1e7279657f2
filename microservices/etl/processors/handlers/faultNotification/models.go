package faultNotification

import "time"

// FaultNotificationMessage represents the incoming fault notification message structure
type FaultNotificationMessage struct {
	MessageVersion string    `json:"MessageVersion"`
	MessageTime    time.Time `json:"MessageTime"`
	DeviceId       string    `json:"DeviceId"`
	FaultedAt      time.Time `json:"FaultedAt"`
	FaultReason    string    `json:"FaultReason"`
	UserDeviceId   string    `json:"UserDeviceId"`
	UserDeviceName string    `json:"UserDeviceName"`
}

// UserNotificationInfo represents user information for notifications
type UserNotificationInfo struct {
	UserId       int64  `db:"userid"`
	Mobile       string `db:"mobile"`
	IANATimezone string `db:"ianatimezone"`
}

// NotificationMessage represents the outgoing notification message structure
type NotificationMessage struct {
	Type     string                 `json:"type"`
	Payload  map[string]interface{} `json:"payload"`
	Metadata map[string]interface{} `json:"metadata"`
}
