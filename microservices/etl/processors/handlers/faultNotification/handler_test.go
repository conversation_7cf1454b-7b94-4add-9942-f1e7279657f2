package faultNotification

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/connect"
)

// Mock implementations for handler testing
type mockService struct {
	mock.Mock
}

func (m *mockService) ProcessFaultNotification(ctx context.Context, faultMessage FaultNotificationMessage) error {
	args := m.Called(ctx, faultMessage)
	return args.Error(0)
}

type mockConnector struct {
	mock.Mock
}

func (m *mockConnector) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*connect.Connections), args.Error(1)
}

type mockCreateService struct {
	mock.Mock
}

func (m *mockCreateService) CreateService(connections *connect.Connections) Service {
	args := m.Called(connections)
	return args.Get(0).(Service)
}

func TestCreateHandler(t *testing.T) {
	tests := []struct {
		name           string
		message        FaultNotificationMessage
		setupMocks     func(*mockConnector, *mockCreateService, *mockService)
		expectedResult string // "ack" or "nack"
	}{
		{
			name: "successful message processing",
			message: FaultNotificationMessage{
				DeviceId:       "123",
				FaultReason:    "Device offline",
				UserDeviceId:   "DEV123",
				UserDeviceName: "Test Device",
				FaultedAt:      time.Now(),
			},
			setupMocks: func(connector *mockConnector, createSvc *mockCreateService, svc *mockService) {
				connections := &connect.Connections{}
				connector.On("GetConnections", mock.Anything).Return(connections, nil)
				createSvc.On("CreateService", connections).Return(svc)
				svc.On("ProcessFaultNotification", mock.Anything, mock.Anything).Return(nil)
			},
			expectedResult: "ack",
		},
		{
			name: "invalid message - missing device ID",
			message: FaultNotificationMessage{
				DeviceId:    "", // Missing required field
				FaultReason: "Device offline",
			},
			setupMocks: func(connector *mockConnector, createSvc *mockCreateService, svc *mockService) {
				connections := &connect.Connections{}
				connector.On("GetConnections", mock.Anything).Return(connections, nil)
				createSvc.On("CreateService", connections).Return(svc)
				// Service should not be called for invalid messages
			},
			expectedResult: "ack", // Invalid messages should be acked to prevent infinite retries
		},
		{
			name: "retryable error",
			message: FaultNotificationMessage{
				DeviceId:    "123",
				FaultReason: "Device offline",
			},
			setupMocks: func(connector *mockConnector, createSvc *mockCreateService, svc *mockService) {
				connections := &connect.Connections{}
				connector.On("GetConnections", mock.Anything).Return(connections, nil)
				createSvc.On("CreateService", connections).Return(svc)
				svc.On("ProcessFaultNotification", mock.Anything, mock.Anything).Return(ErrUserLookupFailed)
			},
			expectedResult: "nack", // Retryable errors should be nacked
		},
		{
			name: "non-retryable error",
			message: FaultNotificationMessage{
				DeviceId:    "invalid",
				FaultReason: "Device offline",
			},
			setupMocks: func(connector *mockConnector, createSvc *mockCreateService, svc *mockService) {
				connections := &connect.Connections{}
				connector.On("GetConnections", mock.Anything).Return(connections, nil)
				createSvc.On("CreateService", connections).Return(svc)
				svc.On("ProcessFaultNotification", mock.Anything, mock.Anything).Return(ErrInvalidDeviceId)
			},
			expectedResult: "ack", // Non-retryable errors should be acked
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockConnector := &mockConnector{}
			mockCreateSvc := &mockCreateService{}
			mockSvc := &mockService{}
			tt.setupMocks(mockConnector, mockCreateSvc, mockSvc)

			// Create handler dependencies
			deps := HandlerDeps{
				Connector: mockConnector.GetConnections,
				CreateService: func(connections *connect.Connections) Service {
					return mockCreateSvc.CreateService(connections)
				},
			}

			// This test verifies the handler creation and dependency injection
			handler := createHandler(deps)
			assert.NotNil(t, handler)

			// Verify mock expectations
			// Note: Full integration testing would require a more complex setup
			// with actual pubsub subscription mocking
		})
	}
}

func TestIsRetryableError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "retryable database error",
			err:      ErrUserLookupFailed,
			expected: true,
		},
		{
			name:     "retryable publish error",
			err:      ErrPublishNotification,
			expected: true,
		},
		{
			name:     "non-retryable invalid device ID",
			err:      ErrInvalidDeviceId,
			expected: false,
		},
		{
			name:     "non-retryable missing env var",
			err:      ErrMissingWebsiteAppsEnv,
			expected: false,
		},
		{
			name:     "non-retryable invalid fault message",
			err:      ErrInvalidFaultMessage,
			expected: false,
		},
		{
			name:     "unknown error defaults to non-retryable",
			err:      assert.AnError,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isRetryableError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandlerDeps_Interface(t *testing.T) {
	// Ensure HandlerDeps has the expected structure
	deps := HandlerDeps{}
	assert.NotNil(t, deps)
	
	// Verify that Handler variable exists and is properly typed
	assert.NotNil(t, Handler)
}
