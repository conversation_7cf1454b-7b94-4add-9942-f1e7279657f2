package faultNotification

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"unicode/utf8"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/logger"
)

// Service defines the interface for fault notification processing
type Service interface {
	ProcessFaultNotification(ctx context.Context, faultMessage FaultNotificationMessage) error
}

// service implements the Service interface
type service struct {
	persistence PersistenceRepository
	pubsub      *pubsub.Client
}

// NewService creates a new fault notification service
func NewService(persistence PersistenceRepository, pubsub *pubsub.Client) Service {
	return &service{
		persistence: persistence,
		pubsub:      pubsub,
	}
}

// ProcessFaultNotification processes an incoming fault notification message
func (s *service) ProcessFaultNotification(ctx context.Context, faultMessage FaultNotificationMessage) error {
	logger.Infof("Processing fault notification for device: %s", faultMessage.DeviceId)

	// Convert DeviceId to integer (matching AWS implementation)
	deviceId, err := strconv.Atoi(faultMessage.DeviceId)
	if err != nil {
		logger.Errorf("DeviceId is not a valid integer: %s", faultMessage.DeviceId)
		return fmt.Errorf("%w: %s", ErrInvalidDeviceId, faultMessage.DeviceId)
	}

	// Lookup users with device access and notifications enabled
	users, err := s.persistence.GetUsersForDeviceNotifications(deviceId)
	if err != nil {
		return err
	}

	// If no users found, skip notification logic (as per requirements)
	if len(users) == 0 {
		logger.Infof("No users with notifications enabled found for device %d, skipping notifications", deviceId)
		return nil
	}

	// Get WEBSITE_APPS environment variable
	websiteApps := os.Getenv("WEBSITE_APPS")
	if websiteApps == "" {
		logger.Errorf("WEBSITE_APPS environment variable not set")
		return ErrMissingWebsiteAppsEnv
	}

	// Process each eligible user
	for _, user := range users {
		if len(user.Mobile) >= 10 { // Minimum phone number length check (from AWS implementation)
			// Construct notification message using AWS smsNotification logic
			notificationMsg, err := s.constructNotificationMessage(faultMessage, user, websiteApps)
			if err != nil {
				logger.Errorf("Failed to construct notification message for user %d: %v", user.UserId, err)
				continue // Continue processing other users
			}

			// Publish notification message
			if err := s.publishNotification(ctx, notificationMsg); err != nil {
				logger.Errorf("Failed to publish notification for user %d: %v", user.UserId, err)
				continue // Continue processing other users
			}

			logger.Infof("Successfully published notification for user %d (mobile: %s)", user.UserId, user.Mobile)
		} else {
			logger.Warnf("User %d has invalid mobile number length: %s", user.UserId, user.Mobile)
		}
	}

	logger.Infof("Completed processing fault notification for device: %s", faultMessage.DeviceId)
	return nil
}

// constructNotificationMessage creates a notification message using AWS smsNotification logic
func (s *service) constructNotificationMessage(faultMessage FaultNotificationMessage, user UserNotificationInfo, websiteApps string) (NotificationMessage, error) {
	// Replicate AWS smsNotification message format (line 161 in notificationsms.go):
	// "EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s"
	messageText := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s",
		faultMessage.UserDeviceId,
		shortenString(faultMessage.UserDeviceName, 25),
		shortenString(faultMessage.FaultReason, 23),
		websiteApps+faultMessage.DeviceId)

	logger.Debugf("Constructed message (length: %d): %s", len(messageText), messageText)

	// Create notification message using the structure from notifications/handler.go
	notificationMsg := NotificationMessage{
		Type: "sms",
		Payload: map[string]interface{}{
			"to":      user.Mobile,
			"message": messageText,
		},
		Metadata: map[string]interface{}{
			"deviceId":      faultMessage.DeviceId,
			"userId":        user.UserId,
			"faultReason":   faultMessage.FaultReason,
			"faultedAt":     faultMessage.FaultedAt,
			"userTimezone":  user.IANATimezone,
		},
	}

	return notificationMsg, nil
}

// publishNotification publishes the notification message to the notification-alerts-sub topic
func (s *service) publishNotification(ctx context.Context, notificationMsg NotificationMessage) error {
	// Marshal notification message to JSON
	messageData, err := json.Marshal(notificationMsg)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrMessageConstruction, err)
	}

	// Get the notification-alerts-sub topic
	topic := s.pubsub.Topic("notification-alerts-sub")

	// Publish the message
	result := topic.Publish(ctx, &pubsub.Message{
		Data: messageData,
	})

	// Wait for the publish to complete
	_, err = result.Get(ctx)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrPublishNotification, err)
	}

	return nil
}

// shortenString truncates a string to the specified length (from AWS implementation)
func shortenString(s string, maxLength int) string {
	if utf8.RuneCountInString(s) <= maxLength {
		return s
	}
	
	runes := []rune(s)
	if len(runes) > maxLength {
		return string(runes[:maxLength])
	}
	return s
}
