# Fault Notification Implementation Plan

## Overview
This document outlines the implementation plan for the fault notification system that will replace the AWS smsNotification lambda function with a microservices architecture approach.

## Current State Analysis
Based on the codebase analysis, there are two distinct fault notification components in the system:

### 1. Gateway Fault Data Processing (✅ IMPLEMENTED)
**Location**: `microservices/etl/processors/handlers/gateway/faultNotification/`

**Purpose**: Processes device fault data from gateway messages and stores it in the DeviceFault table.

**Components**:
- ✅ **Handler** (`handler.go`) - Processes gateway device data, extracts fault information, and stores in Postgres DeviceFault table
- ✅ **Unit Tests** (`handler_test.go`) - Tests for the gateway fault data processing
- ✅ **Database Integration** - Uses `upsertDeviceFault` function to store fault data in DeviceFault table
- ✅ **BigQuery Integration** - Stores fault notifications in BigQuery via `edihelper.RmsStatusToFaultNotification`

**Functionality**:
- Receives protobuf messages from gateway devices
- Parses RMS status data to extract fault information
- Updates DeviceFault table with current device status
- Stores fault notification records in BigQuery
- Handles unprocessed messages via DLQ

### 2. User Fault Notification Service (❌ NOT IMPLEMENTED)
**Location**: `microservices/etl/processors/handlers/faultNotification/` (EMPTY DIRECTORY)

**Purpose**: Sends SMS notifications to users when device faults occur.

**Current Status**:
- ❌ **Directory is completely empty** - No implementation exists
- ❌ **No service layer** for user notification logic
- ❌ **No handler** for processing fault notification messages
- ❌ **No models** for notification message structures
- ❌ **No persistence layer** for user lookup
- ❌ **No tests** for notification functionality

### 3. Notification Delivery Infrastructure (✅ IMPLEMENTED)
**Location**: `microservices/etl/processors/handlers/notifications/`

**Purpose**: Delivers notifications via SMS and stores them in BigQuery.

**Components**:
- ✅ **Handler** (`handler.go`) - Processes notification messages from Pub/Sub
- ✅ **Twilio Service** (`twilio/service.go`) - Sends SMS messages via Twilio API
- ✅ **BigQuery Integration** - Stores notification delivery records
- ✅ **Message Processing** - Handles notification-alerts-sub topic messages

## Missing Implementation: User Fault Notification Service

The user fault notification service needs to be built from scratch. This service should:

1. **Listen for fault events** - Either from the DeviceFault table changes or from a dedicated Pub/Sub topic
2. **Look up affected users** - Query users who have access to the faulted device and have SMS notifications enabled
3. **Send notifications** - Publish notification messages to the notification-alerts-sub topic for SMS delivery
4. **Match AWS behavior** - Replicate the exact message format and logic from the AWS smsNotification lambda

## Implementation Strategy

### Option 1: Database Trigger Approach (Recommended)
Create a database trigger on the DeviceFault table that publishes messages to a Pub/Sub topic when fault status changes.

**Advantages**:
- Real-time fault detection
- Leverages existing gateway fault processing
- Clean separation of concerns
- Reliable delivery via Pub/Sub

**Implementation Steps**:
1. Create database trigger on DeviceFault table for IsFaulted status changes
2. Trigger publishes to fault-notification-trigger topic
3. User notification service subscribes to fault-notification-trigger topic
4. Service looks up users and publishes to notification-alerts-sub topic

### Option 2: Polling Approach
Create a service that periodically polls the DeviceFault table for new faults.

**Disadvantages**:
- Higher latency
- More complex state management
- Potential for duplicate notifications

## Required Implementation Tasks

### Phase 1: Core Service Implementation

#### Task 1.1: Create User Notification Service Structure
**Directory**: `microservices/etl/processors/handlers/faultNotification/`

**Files to Create**:
- `models.go` - Data structures for fault notifications and user info
- `errors.go` - Error definitions
- `persistence_repository.go` - Database operations for user lookup
- `service.go` - Business logic for notification processing
- `handler.go` - Pub/Sub message handler
- `go.mod` - Module definition

#### Task 1.2: Database Schema Analysis
**Requirements**:
- Analyze DeviceFault table structure
- Understand User and UserDevice table relationships
- Design trigger logic for fault status changes

#### Task 1.3: Message Flow Design
**Requirements**:
- Define message structure for fault notification triggers
- Ensure compatibility with existing notification infrastructure
- Match AWS smsNotification message format exactly

### Phase 2: Database Integration

#### Task 2.1: Create Database Trigger (Recommended Approach)
**Requirements**:
- Create PostgreSQL trigger on DeviceFault table
- Trigger fires when IsFaulted column changes from false to true
- Publishes message to fault-notification-trigger Pub/Sub topic
- Include device information and fault details in message

**SQL Trigger Logic**:
```sql
CREATE OR REPLACE FUNCTION notify_fault_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only trigger when IsFaulted changes from false to true
    IF OLD.IsFaulted = false AND NEW.IsFaulted = true THEN
        -- Publish to Pub/Sub topic via pg_notify or external function
        PERFORM pg_notify('fault_notification',
            json_build_object(
                'device_id', NEW.DeviceIdentifier,
                'fault_reason', NEW.Fault,
                'faulted_at', NEW.MonitorTime,
                'device_model', NEW.DeviceModel
            )::text
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER fault_notification_trigger
    AFTER UPDATE ON DeviceFault
    FOR EACH ROW
    EXECUTE FUNCTION notify_fault_change();
```

#### Task 2.2: User Lookup Implementation
**Requirements**:
- Implement user query based on AWS smsNotification logic
- Query users with device access and SMS notifications enabled
- Handle timezone information for user notifications

**SQL Query** (from AWS implementation):
```sql
SELECT u.Id as userid, u.Mobile as mobile, u.IANATimezone as ianatimezone
FROM {{User}} u
INNER JOIN {{UserDevice}} ud ON u.Id = ud.UserId
WHERE ud.DeviceId = $1
  AND u.Mobile IS NOT NULL
  AND u.IsEnabled = 1
  AND u.NotificationSmsEnabled = 1
```

### Phase 3: Service Implementation

#### Task 3.1: Message Processing Logic
**Requirements**:
- Process fault notification trigger messages
- Look up affected users for each device
- Construct SMS messages matching AWS format exactly
- Publish to notification-alerts-sub topic

#### Task 3.2: Error Handling and Retry Logic
**Requirements**:
- Handle database connection failures
- Implement retry logic for transient errors
- Send failed messages to DLQ for investigation
- Log all operations for debugging

### Phase 4: Testing Implementation

#### Task 4.1: Unit Tests
**Files to Create**:
- `service_test.go` - Test business logic and message construction
- `persistence_repository_test.go` - Test database operations
- `handler_test.go` - Test Pub/Sub message handling

**Test Coverage Requirements**:
- All error scenarios (database failures, invalid data, etc.)
- Message construction logic matching AWS format
- User lookup scenarios (no users, multiple users, database errors)
- Pub/Sub publishing success and failure cases

#### Task 4.2: Integration Tests
**File**: `microservices/testing/integration/fault_notification_test.go`

**Test Scenarios**:
1. End-to-end fault notification flow
2. Database trigger functionality
3. User lookup and notification delivery
4. BigQuery persistence verification
5. Error handling and DLQ processing

### Phase 5: Deployment and Configuration

#### Task 5.1: Environment Configuration
**Requirements**:
- Add `WEBSITE_APPS = https://www.synapse-its.app/apps/` environment variable
- Configure Pub/Sub topics and subscriptions
- Set up database trigger in production

#### Task 5.2: Monitoring and Alerting
**Requirements**:
- Set up metrics for notification delivery success/failure rates
- Monitor database trigger performance
- Alert on high error rates or processing delays

## Architecture Decisions

### Message Flow Architecture
```
DeviceFault Table → Database Trigger → fault-notification-trigger Topic
                                            ↓
User Notification Service → User Lookup → notification-alerts-sub Topic
                                            ↓
Notification Handler → Twilio SMS → BigQuery Storage
```

### Clean Architecture Implementation
Following the pattern from `microservices/etl/processors/handlers/synapse/purgeExpired/`:

1. **Separation of Concerns**:
   - Handler: Pub/Sub message processing
   - Service: Business logic for user notifications
   - Repository: Database operations for user lookup
   - Models: Data structures and message formats

2. **Dependency Injection**:
   - Interface-based design for testability
   - Constructor injection pattern
   - Mock-friendly for unit testing

3. **Error Handling**:
   - Structured error types with context
   - Retry logic for transient failures
   - Comprehensive logging for debugging

## Current Implementation Status

### ✅ Completed Components
1. **Gateway Fault Processing**: Fully implemented in `microservices/etl/processors/handlers/gateway/faultNotification/`
   - Processes device fault data from gateway messages
   - Stores fault information in DeviceFault table
   - Includes comprehensive unit tests
   - Integrates with BigQuery for fault notification records

2. **Notification Delivery Infrastructure**: Fully implemented in `microservices/etl/processors/handlers/notifications/`
   - Processes notification messages from Pub/Sub
   - Sends SMS via Twilio API
   - Stores delivery records in BigQuery
   - Handles various notification types

3. **AWS Reference Implementation**: Available in `notificationsms.go`
   - Provides exact message format to replicate
   - Shows user lookup logic and filtering criteria
   - Demonstrates SMS message construction

### ❌ Missing Components
1. **User Fault Notification Service**: Completely missing
   - No implementation in `microservices/etl/processors/handlers/faultNotification/`
   - No service to connect fault detection with user notifications
   - No database trigger to detect fault status changes
   - No user lookup implementation

2. **Integration Between Components**: Missing connection
   - No mechanism to trigger user notifications when faults occur
   - No bridge between DeviceFault table updates and user notification system

## Implementation Priority

### Critical Priority (Immediate - Required for MVP)
1. **Create User Notification Service Structure**
   - Set up directory structure and Go module
   - Implement basic models and error definitions
   - Create service interface and handler skeleton

2. **Implement Database Trigger**
   - Create PostgreSQL trigger on DeviceFault table
   - Set up Pub/Sub topic for fault notifications
   - Test trigger functionality

3. **Implement User Lookup Logic**
   - Create persistence repository for user queries
   - Implement exact SQL logic from AWS implementation
   - Handle edge cases (no users, invalid devices)

### High Priority (Sprint 1)
1. **Complete Service Implementation**
   - Implement message construction matching AWS format
   - Add Pub/Sub publishing to notification-alerts-sub
   - Implement comprehensive error handling

2. **Unit Test Coverage**
   - Test all service components
   - Mock external dependencies
   - Achieve 100% code coverage

### Medium Priority (Sprint 2)
1. **Integration Testing**
   - End-to-end fault notification flow
   - Database trigger testing
   - Performance validation

2. **Environment Configuration**
   - Set up WEBSITE_APPS environment variable
   - Configure Pub/Sub topics and subscriptions
   - Deploy database trigger to production

### Low Priority (Future Enhancements)
1. **Advanced Features**
   - Notification preferences per user
   - Multiple notification channels (email, push)
   - Notification scheduling and throttling

2. **Monitoring and Analytics**
   - Detailed metrics and dashboards
   - Performance optimization
   - Advanced alerting rules

## Key Implementation Decisions

### Database Trigger vs. Polling
**Decision**: Use database trigger approach
**Rationale**:
- Real-time fault detection
- Leverages existing gateway fault processing
- Avoids complex polling logic and state management
- Reliable message delivery via Pub/Sub

### Message Format Compatibility
**Decision**: Exactly replicate AWS smsNotification format
**Rationale**:
- Ensures seamless user experience during migration
- Reduces risk of user confusion
- Allows for gradual rollout and easy rollback

### Service Architecture
**Decision**: Follow clean architecture pattern from existing handlers
**Rationale**:
- Consistent with codebase patterns
- Highly testable with dependency injection
- Maintainable and extensible

## Risk Mitigation

### Database Performance Risks
- **Risk**: User lookup queries may be slow with large user bases
- **Mitigation**: Ensure proper indexes on UserDevice.DeviceId and User.Id
- **Monitoring**: Track query execution times and optimize as needed

### Message Delivery Reliability Risks
- **Risk**: Pub/Sub messages may be lost or duplicated
- **Mitigation**: Implement idempotency keys and DLQ handling
- **Monitoring**: Track message delivery success rates

### Migration Risks
- **Risk**: Differences in behavior between AWS and microservices implementation
- **Mitigation**: Comprehensive testing with real data and gradual rollout
- **Rollback**: Maintain AWS lambda as backup during initial deployment

## Success Metrics

### Functional Metrics
- User notification service successfully processes fault events
- Message format exactly matches AWS smsNotification implementation
- Database trigger reliably detects fault status changes
- Integration tests validate end-to-end functionality

### Performance Metrics
- Fault detection latency < 1 second (database trigger)
- User lookup query response time < 100ms
- Notification message processing < 500ms
- SMS delivery success rate matches current AWS performance

### Operational Metrics
- Zero data loss during fault notification processing
- Proper error handling and DLQ usage for failed messages
- Comprehensive logging for debugging and monitoring
- Successful deployment without affecting existing functionality

## Next Steps for Implementation

### Immediate Actions Required

1. **Create Service Directory Structure**
   ```bash
   mkdir -p microservices/etl/processors/handlers/faultNotification
   cd microservices/etl/processors/handlers/faultNotification
   ```

2. **Initialize Go Module**
   ```bash
   go mod init synapse-its.com/etl/processors/handlers/faultNotification
   ```

3. **Implement Core Files** (in order of dependency):
   - `models.go` - Data structures
   - `errors.go` - Error definitions
   - `persistence_repository.go` - Database operations
   - `service.go` - Business logic
   - `handler.go` - Pub/Sub message processing
   - `*_test.go` - Unit tests for each component

4. **Set Up Database Trigger**
   - Create PostgreSQL trigger function
   - Test trigger with DeviceFault table updates
   - Configure Pub/Sub topic for trigger messages

5. **Integration Testing**
   - Test complete fault notification flow
   - Validate message format compatibility
   - Verify SMS delivery and BigQuery persistence

### Critical Dependencies

1. **Database Access**: Requires connection to Postgres with User, UserDevice, and DeviceFault tables
2. **Pub/Sub Topics**: Needs fault-notification-trigger and notification-alerts-sub topics
3. **Environment Variables**: Requires WEBSITE_APPS configuration
4. **AWS Reference**: Must match exact behavior from notificationsms.go implementation

## Summary

The fault notification implementation requires building a complete user notification service from scratch. The current state shows:

### What Exists ✅
- **Gateway fault processing**: Detects and stores device faults in DeviceFault table
- **Notification delivery**: Sends SMS via Twilio and stores in BigQuery
- **AWS reference**: Complete implementation in notificationsms.go to replicate

### What's Missing ❌
- **User notification service**: No implementation exists in the intended directory
- **Database trigger**: No mechanism to detect fault status changes
- **Integration**: No connection between fault detection and user notifications

### Implementation Approach
1. **Database trigger** on DeviceFault table to detect IsFaulted status changes
2. **User notification service** to process trigger events and look up affected users
3. **Message publishing** to notification-alerts-sub topic for SMS delivery
4. **Exact replication** of AWS smsNotification message format and logic

The implementation should follow the clean architecture pattern established in other handlers and ensure seamless migration from the AWS lambda function.

## Code Quality Standards

### Testing Requirements
- Minimum 100% line coverage for new code
- All error paths must be tested
- Integration tests must use real dependencies
- Mock all external dependencies in unit tests
- Follow existing test patterns and naming conventions

### Code Review Checklist
- [ ] Follows clean architecture principles
- [ ] Proper error handling and logging
- [ ] Database queries are parameterized
- [ ] Environment variables are validated
- [ ] Unit tests cover all code paths
- [ ] Integration tests verify end-to-end flow
- [ ] Documentation is updated
- [ ] Performance considerations addressed

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Database indexes optimized
- [ ] Pub/Sub topics and subscriptions configured
- [ ] Monitoring and alerting set up
- [ ] Rollback plan documented
- [ ] Performance benchmarks established
