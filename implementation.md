# Fault Notification Implementation Plan

## Overview
This document outlines the implementation plan for the fault notification system that will replace the AWS smsNotification lambda function with a microservices architecture approach.

## Current State Analysis
Based on the codebase analysis, the fault notification system is partially implemented with the following components already in place:

### Existing Components
1. **Service Layer** (`microservices/etl/processors/handlers/faultNotification/service.go`)
   - ✅ Service interface and implementation
   - ✅ Message construction logic matching AWS implementation
   - ✅ Pub/Sub publishing to notification-alerts-sub topic
   - ✅ Error handling and logging

2. **Models** (`microservices/etl/processors/handlers/faultNotification/models.go`)
   - ✅ FaultNotificationMessage struct
   - ✅ UserNotificationInfo struct  
   - ✅ NotificationMessage struct

3. **Handler** (`microservices/etl/processors/handlers/faultNotification/handler.go`)
   - ✅ Pub/Sub message handler
   - ✅ Error handling and retry logic
   - ✅ Service dependency injection

4. **Unit Tests** (`microservices/etl/processors/handlers/faultNotification/handler_test.go`)
   - ✅ Basic handler tests with mocks

5. **Notification Processing** (`microservices/etl/processors/handlers/notifications/handler.go`)
   - ✅ SMS notification delivery via Twilio
   - ✅ BigQuery persistence for notification messages

### Missing Components
1. **PersistenceRepository Interface and Implementation**
2. **Error Definitions**
3. **Complete Unit Test Coverage**
4. **Integration Tests**
5. **Environment Variable Configuration**

## Implementation Tasks

### Phase 1: Core Infrastructure (Missing Components)

#### Task 1.1: Create PersistenceRepository Interface and Implementation
**File**: `microservices/etl/processors/handlers/faultNotification/persistence_repository.go`

**Requirements**:
- Implement `PersistenceRepository` interface with `GetUsersForDeviceNotifications(deviceId int) ([]UserNotificationInfo, error)` method
- Use JOIN query: `FROM {{UserDevice}} LEFT JOIN {{User}}`
- Filter for users with device access and notifications enabled
- Follow clean architecture pattern from `microservices/etl/processors/handlers/synapse/purgeExpired/`

**SQL Query Logic** (based on AWS implementation):
```sql
SELECT u.Id as userid, u.Mobile as mobile, u.IANATimezone as ianatimezone
FROM {{User}} u 
INNER JOIN {{UserDevice}} ud ON u.Id = ud.UserId 
WHERE ud.DeviceId = $1 
  AND u.Mobile IS NOT NULL 
  AND u.IsEnabled = 1 
  AND u.NotificationSmsEnabled = 1
```

#### Task 1.2: Create Error Definitions
**File**: `microservices/etl/processors/handlers/faultNotification/errors.go`

**Requirements**:
- Define all error constants used in service.go
- Follow error pattern from other handlers
- Include: `ErrInvalidDeviceId`, `ErrUserLookupFailed`, `ErrPublishNotification`, `ErrMessageConstruction`, `ErrMissingWebsiteAppsEnv`, `ErrInvalidFaultMessage`

#### Task 1.3: Environment Variable Configuration
**Requirements**:
- Add `WEBSITE_APPS = https://www.synapse-its.app/apps/` to environment configuration
- Ensure variable is available in deployment configuration

### Phase 2: Testing Implementation

#### Task 2.1: Complete Unit Test Coverage
**Files**: 
- `microservices/etl/processors/handlers/faultNotification/service_test.go`
- `microservices/etl/processors/handlers/faultNotification/persistence_repository_test.go`

**Requirements**:
- Achieve 100% code coverage for new code paths
- Test all error scenarios
- Test message construction logic
- Test user lookup scenarios (no users, multiple users, database errors)
- Test Pub/Sub publishing scenarios
- Follow testing patterns from `microservices/etl/processors/handlers/synapse/purgeExpired/`

#### Task 2.2: Integration Tests
**File**: `microservices/testing/integration/fault_notification_test.go`

**Requirements**:
- Test end-to-end message flow:
  1. Fault notification message received
  2. Users queried from Postgres
  3. Notification messages constructed
  4. Messages published to notification-alerts-sub
  5. Notification handler processes messages
  6. SMS sent via Twilio
  7. Data persisted in BigQuery
- Use real Postgres, Pub/Sub, and BigQuery connections
- Follow integration test patterns from `microservices/testing/integration/`

### Phase 3: Validation and Documentation

#### Task 3.1: Message Format Validation
**Requirements**:
- Verify notification message structure matches `microservices/etl/processors/handlers/notifications/handler.go`
- Ensure SMS message format matches AWS implementation exactly
- Validate BigQuery schema compatibility

#### Task 3.2: Performance Testing
**Requirements**:
- Test with multiple users per device
- Test with high message volume
- Verify no memory leaks or resource issues

## Architecture Decisions

### Clean Architecture Implementation
Following the pattern established in `microservices/etl/processors/handlers/synapse/purgeExpired/`:

1. **Separation of Concerns**:
   - Handler: Message processing and routing
   - Service: Business logic orchestration  
   - Repository: Data access abstraction
   - Models: Data structures

2. **Dependency Injection**:
   - Interfaces for all external dependencies
   - Constructor injection pattern
   - Testable with mocks

3. **Error Handling**:
   - Structured error types
   - Retry logic for transient failures
   - Proper logging at all levels

### Database Query Strategy
- Use parameterized queries to prevent SQL injection
- Implement proper connection handling via `connect.DatabaseExecutor`
- Follow existing table naming conventions with `{{TableName}}` syntax
- Handle empty result sets gracefully

### Message Publishing Strategy
- Use synchronous publishing with `result.Get(ctx)` for reliability
- Implement proper error handling for publish failures
- Include comprehensive metadata for debugging and monitoring

## Acceptance Criteria Validation

### ✅ Completed Criteria
1. **Fault data formatting**: Matches AWS smsNotification behavior (line 161)
2. **Message schema**: Uses structure from notifications/handler.go
3. **Pub/Sub publishing**: Publishes to notification-alerts-sub topic
4. **Error handling**: Logs errors without crashing system
5. **User filtering**: Service layer ready for user lookup implementation

### 🔄 In Progress Criteria  
1. **User lookup**: Needs PersistenceRepository implementation
2. **Environment variable**: Needs WEBSITE_APPS configuration
3. **Unit tests**: Needs complete test coverage
4. **Integration tests**: Needs end-to-end test implementation

### ⏳ Pending Criteria
1. **BigQuery persistence**: Depends on integration test validation
2. **Message consumption**: Depends on integration test validation

## Implementation Priority

### High Priority (Immediate)
1. Create PersistenceRepository implementation
2. Create error definitions
3. Configure WEBSITE_APPS environment variable
4. Complete unit tests

### Medium Priority (Next Sprint)
1. Create integration tests
2. Performance testing
3. Documentation updates

### Low Priority (Future)
1. Monitoring and alerting enhancements
2. Performance optimizations
3. Additional notification types (email, push)

## Risk Mitigation

### Database Performance
- Index on UserDevice.DeviceId and User.Id for optimal JOIN performance
- Monitor query execution time
- Consider caching for frequently accessed devices

### Message Delivery Reliability
- Implement dead letter queue handling
- Monitor Pub/Sub topic metrics
- Set up alerting for failed message deliveries

### Backward Compatibility
- Maintain exact message format compatibility with AWS implementation
- Gradual rollout with feature flags
- Rollback plan to AWS lambda if needed

## Success Metrics

### Functional Metrics
- 100% unit test coverage for new code
- Integration tests passing consistently
- Message format matches AWS implementation exactly
- All acceptance criteria met

### Performance Metrics
- Message processing latency < 500ms
- Database query response time < 100ms
- Pub/Sub publish success rate > 99.9%
- SMS delivery success rate matches current AWS performance

### Operational Metrics
- Zero system crashes due to fault notification processing
- Proper error logging and monitoring
- Successful deployment without rollback

## Detailed Implementation Steps

### Step 1: Create Missing Repository Implementation

**File**: `microservices/etl/processors/handlers/faultNotification/persistence_repository.go`

```go
package faultNotification

import (
    "fmt"
    "synapse-its.com/shared/connect"
)

// PersistenceRepository defines the interface for database operations
type PersistenceRepository interface {
    GetUsersForDeviceNotifications(deviceId int) ([]UserNotificationInfo, error)
}

// repository implements the PersistenceRepository interface
type repository struct {
    db connect.DatabaseExecutor
}

// NewPersistenceRepository creates a new persistence repository
func NewPersistenceRepository(db connect.DatabaseExecutor) PersistenceRepository {
    return &repository{db: db}
}

// GetUsersForDeviceNotifications retrieves users with device access and notifications enabled
func (r *repository) GetUsersForDeviceNotifications(deviceId int) ([]UserNotificationInfo, error) {
    query := `
        SELECT u.Id as userid, u.Mobile as mobile, u.IANATimezone as ianatimezone
        FROM {{User}} u
        INNER JOIN {{UserDevice}} ud ON u.Id = ud.UserId
        WHERE ud.DeviceId = $1
          AND u.Mobile IS NOT NULL
          AND u.IsEnabled = 1
          AND u.NotificationSmsEnabled = 1
    `

    var users []UserNotificationInfo
    err := r.db.QueryGenericSlice(&users, query, deviceId)
    if err != nil {
        return nil, fmt.Errorf("%w: %v", ErrUserLookupFailed, err)
    }

    return users, nil
}
```

### Step 2: Create Error Definitions

**File**: `microservices/etl/processors/handlers/faultNotification/errors.go`

```go
package faultNotification

import "errors"

var (
    ErrInvalidDeviceId         = errors.New("invalid device ID")
    ErrUserLookupFailed        = errors.New("failed to lookup users for device notifications")
    ErrPublishNotification     = errors.New("failed to publish notification message")
    ErrMessageConstruction     = errors.New("failed to construct notification message")
    ErrMissingWebsiteAppsEnv   = errors.New("WEBSITE_APPS environment variable not set")
    ErrInvalidFaultMessage     = errors.New("invalid fault notification message")
)
```

### Step 3: Complete Unit Tests

**File**: `microservices/etl/processors/handlers/faultNotification/service_test.go`

Key test scenarios:
- ProcessFaultNotification with valid data
- ProcessFaultNotification with invalid device ID
- ProcessFaultNotification with no users found
- ProcessFaultNotification with database error
- ProcessFaultNotification with missing WEBSITE_APPS
- constructNotificationMessage with various inputs
- publishNotification success and failure scenarios

**File**: `microservices/etl/processors/handlers/faultNotification/persistence_repository_test.go`

Key test scenarios:
- GetUsersForDeviceNotifications with multiple users
- GetUsersForDeviceNotifications with no users
- GetUsersForDeviceNotifications with database error
- SQL query parameter validation

### Step 4: Integration Test Implementation

**File**: `microservices/testing/integration/fault_notification_test.go`

Test flow:
1. Set up test database with users and devices
2. Set up test Pub/Sub topics
3. Publish fault notification message
4. Verify user lookup occurs
5. Verify notification messages are published
6. Verify notification handler processes messages
7. Verify BigQuery persistence
8. Clean up test data

### Step 5: Environment Configuration

Add to deployment configuration:
```yaml
environment:
  WEBSITE_APPS: "https://www.synapse-its.app/apps/"
```

## Code Quality Standards

### Testing Requirements
- Minimum 100% line coverage for new code
- All error paths must be tested
- Integration tests must use real dependencies
- Mock all external dependencies in unit tests
- Follow existing test patterns and naming conventions

### Code Review Checklist
- [ ] Follows clean architecture principles
- [ ] Proper error handling and logging
- [ ] Database queries are parameterized
- [ ] Environment variables are validated
- [ ] Unit tests cover all code paths
- [ ] Integration tests verify end-to-end flow
- [ ] Documentation is updated
- [ ] Performance considerations addressed

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Database indexes optimized
- [ ] Pub/Sub topics and subscriptions configured
- [ ] Monitoring and alerting set up
- [ ] Rollback plan documented
- [ ] Performance benchmarks established
