[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Fault Notification System Implementation DESCRIPTION:Implement a complete fault notification system that processes incoming fault notification messages, queries users with device access and notifications enabled, constructs notification messages, and publishes them to the notification delivery pipeline.
--[x] NAME:Create Fault Notification Handler Package Structure DESCRIPTION:Create the package structure following clean architecture pattern similar to purgeExpired handler with handler.go, service.go, persistence_repository.go, models.go, and errors.go files
--[x] NAME:Implement User Lookup Repository DESCRIPTION:Create persistence repository with method to query Postgres for users with device access and notifications enabled using JOIN between User and UserDevice tables
--[x] NAME:Implement Notification Message Construction Service DESCRIPTION:Create service to parse fault data and construct notification messages using logic from AWS smsNotification function, including message formatting and WEBSITE_APPS environment variable
--[x] NAME:Implement Pub/Sub Publisher DESCRIPTION:Create publisher component to marshal notification messages as JSON and publish to notification-alerts-sub Pub/Sub topic
--[x] NAME:Create Unit Tests DESCRIPTION:Implement comprehensive unit tests covering 100% of new code paths including user lookup, message construction, and publishing logic
--[x] NAME:Create Integration Tests DESCRIPTION:Implement integration tests under microservices/testing/ that verify message formation, publishing to correct topic, ETL processing, and BigQuery persistence
--[x] NAME:Add Environment Variable Configuration DESCRIPTION:Add WEBSITE_APPS environment variable configuration and update deployment configurations